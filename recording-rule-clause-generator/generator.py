# this file takes a list of metrics and generates a recording rule clause for each metric by querying grafana.
# our base labels should always be included (product_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) and the diff should be added to the base labels 

import requests
import json
import sys
import os
import time
import random
import string
import argpar<PERSON>
from typing import List, Dict, Any

# id 160 is kr-prometheus
GRAFANA_TOKEN = os.getenv("GRAFANA_TOKEN")
def get_metric_labels(metric_name: str) -> List[str]:
    """Query grafana for the labels of a metric"""
    url = f"https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/label/{metric_name}/values"
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {GRAFANA_TOKEN}"}
    response = requests.get(url, headers=headers, verify=False)
    print("\n")
    print(response.json())
    if response.status_code != 200:
        raise Exception(f"Error fetching labels for metric {metric_name}: {response.text}")
    return response.json()["data"]

def generate_recording_rule_clause(metric_name: str, labels: List[str]) -> str:
    """Generate a recording rule clause for a metric"""
    return f"sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id, {', '.join(labels)}) ({metric_name})"

def main():
    k = get_metric_labels("cwp_api_latency_seconds_bucket")
    print(k)


if __name__ == "__main__":
    main()