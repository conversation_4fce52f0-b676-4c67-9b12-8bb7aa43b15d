# this file takes a list of metrics and generates a recording rule clause for each metric by querying grafana.
# our base labels should always be included (product_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) and the diff should be added to the base labels 

import requests
import json
import sys
import os
import time
import random
import string
import argparse
from typing import List, Dict, Any

# id 160 is kr-prometheus
GRAFANA_TOKEN = os.getenv("GRAFANA_TOKEN")
def get_metric_labels(metric_name: str) -> List[str]:
    # use query api to run query
    url = f"https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/series?match[]={metric_name}"
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {GRAFANA_TOKEN}"}
    response = requests.get(url, headers=headers, verify=False)
    print(f"Raw response: {response.text}")
    if response.status_code != 200:
        raise Exception(f"Failed to get metric labels: {response.text}")

    data = response.json()
    print(f"Parsed response: {data}")

    if data.get('status') != 'success':
        raise Exception(f"API returned error status: {data}")

    series_data = data.get('data', [])
    print(f"Number of series found: {len(series_data)}")

    if not series_data:
        print(f"No series found for metric: {metric_name}")
        return []

    # Extract all unique labels from all series
    all_labels = set()
    for series in series_data:
        if isinstance(series, dict):
            all_labels.update(series.keys())

    return list(all_labels)

def main():
    k = get_metric_labels("cwp_api_latency_seconds_bucket")
    print(k)


if __name__ == "__main__":
    main()