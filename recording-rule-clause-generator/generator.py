# this file takes a list of metrics and generates a recording rule clause for each metric by querying grafana.
# our base labels should always be included (product_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) and the diff should be added to the base labels 

import requests
import json
import sys
import os
import time
import random
import string
import argparse
from typing import List, Dict, Any

# id 160 is kr-prometheus
GRAFANA_TOKEN = os.getenv("GRAFANA_TOKEN")
def get_metric_labels(metric_name: str) -> List[str]:
    # use query api to run query
    import urllib.parse

    # URL encode the metric name properly
    encoded_metric = urllib.parse.quote(metric_name)
    url = f"https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/series?match[]={encoded_metric}"

    print(f"Request URL: {url}")
    print(f"Metric name: {metric_name}")
    print(f"Encoded metric: {encoded_metric}")

    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {GRAFANA_TOKEN}"}
    response = requests.get(url, headers=headers, verify=False)

    print(f"Response status: {response.status_code}")
    print(f"Raw response: {response.text}")

    if response.status_code != 200:
        raise Exception(f"Failed to get metric labels: {response.text}")

    data = response.json()

    if data.get('status') != 'success':
        raise Exception(f"API returned error status: {data}")

    series_data = data.get('data', [])
    print(f"Number of series found: {len(series_data)}")

    if not series_data:
        print(f"No series found for metric: {metric_name}")
        # Let's try a broader search to see what metrics are available
        print("Trying a broader search...")
        return try_broader_search(metric_name)

    # Extract all unique labels from all series
    all_labels = set()
    for series in series_data:
        if isinstance(series, dict):
            all_labels.update(series.keys())

    return list(all_labels)

def try_broader_search(original_metric: str) -> List[str]:
    """Try different variations of the metric name to find what's available"""
    import urllib.parse

    # Try with wildcards
    variations = [
        f"{original_metric}*",
        f"*{original_metric}*",
        original_metric.replace("_bucket", ""),
        "up",  # This should always exist as a test
    ]

    for variation in variations:
        print(f"\nTrying variation: {variation}")
        encoded_metric = urllib.parse.quote(variation)
        url = f"https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/series?match[]={encoded_metric}"

        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {GRAFANA_TOKEN}"}
        response = requests.get(url, headers=headers, verify=False)

        if response.status_code == 200:
            data = response.json()
            series_data = data.get('data', [])
            print(f"Found {len(series_data)} series for '{variation}'")

            if series_data:
                print("Sample series:")
                for i, series in enumerate(series_data[:3]):  # Show first 3
                    print(f"  {i+1}: {series}")
                return []  # Return empty for now, just debugging

    return []

def test_different_endpoints():
    """Test different API endpoints to see what works"""
    import urllib.parse

    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {GRAFANA_TOKEN}"}

    # Test different endpoints
    endpoints = [
        # Original series endpoint
        "https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/series?match[]=up",

        # Try query endpoint instead
        "https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/query?query=up",

        # Try label values endpoint
        "https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/label/__name__/values",

        # Try metadata endpoint
        "https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/metadata",

        # Try targets endpoint
        "https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/targets/metadata",
    ]

    for url in endpoints:
        print(f"\n{'='*60}")
        print(f"Testing: {url}")
        print('='*60)

        try:
            response = requests.get(url, headers=headers, verify=False)
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text[:500]}...")  # First 500 chars

            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, dict) and 'data' in data:
                        print(f"Data length: {len(data.get('data', []))}")
                except:
                    pass

        except Exception as e:
            print(f"Error: {e}")

def main():
    print("Testing different API endpoints...")
    test_different_endpoints()

    print("\n" + "="*60)
    print("Testing original function...")
    print("="*60)
    k = get_metric_labels("cwp_api_latency_seconds_bucket")
    print(k)


if __name__ == "__main__":
    main()